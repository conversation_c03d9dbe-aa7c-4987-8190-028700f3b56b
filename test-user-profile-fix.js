// Test script to verify the user profile update fix
// This script demonstrates the difference between the old and new approach

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://pcfrgvhigvklersufktf.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBjZnJndmhpZ3ZrbGVyc3Vma3RmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3NjA5MzcsImV4cCI6MjA2NDMzNjkzN30.sz7YpgMNQ8AT5PzTBy_MBtPNdE135R7hy2LU7YZO56g';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// OLD APPROACH (PROBLEMATIC) - Using upsert without email
async function oldUpdateUserProfile(userId, updates) {
  try {
    const { error } = await supabase
      .from('users')
      .upsert({
        id: userId,
        ...updates,
        updated_at: new Date().toISOString(),
      });

    if (error) {
      console.error('OLD APPROACH ERROR:', error);
      return false;
    }
    return true;
  } catch (error) {
    console.error('OLD APPROACH EXCEPTION:', error);
    return false;
  }
}

// NEW APPROACH (FIXED) - Using update for existing records
async function newUpdateUserProfile(userId, updates) {
  try {
    const { error } = await supabase
      .from('users')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (error) {
      console.error('NEW APPROACH ERROR:', error);
      return false;
    }
    return true;
  } catch (error) {
    console.error('NEW APPROACH EXCEPTION:', error);
    return false;
  }
}

// NEW APPROACH - Creating user profile with required fields
async function createUserProfile(userId, profileData) {
  try {
    const { error } = await supabase
      .from('users')
      .upsert({
        id: userId,
        ...profileData,
        updated_at: new Date().toISOString(),
      });

    if (error) {
      console.error('CREATE PROFILE ERROR:', error);
      return false;
    }
    return true;
  } catch (error) {
    console.error('CREATE PROFILE EXCEPTION:', error);
    return false;
  }
}

// Test function
async function testFix() {
  console.log('=== Testing User Profile Update Fix ===\n');
  
  const testUserId = 'test-user-' + Date.now();
  
  console.log('1. Testing OLD approach (should fail):');
  const oldResult = await oldUpdateUserProfile(testUserId, {
    last_login: new Date().toISOString(),
  });
  console.log('Old approach result:', oldResult);
  
  console.log('\n2. Testing NEW approach for non-existent user (should fail gracefully):');
  const newResult = await newUpdateUserProfile(testUserId, {
    last_login: new Date().toISOString(),
  });
  console.log('New approach result:', newResult);
  
  console.log('\n3. Testing CREATE profile approach (should work):');
  const createResult = await createUserProfile(testUserId, {
    email: '<EMAIL>',
    display_name: 'Test User',
    last_login: new Date().toISOString(),
  });
  console.log('Create profile result:', createResult);
  
  if (createResult) {
    console.log('\n4. Testing NEW approach for existing user (should work):');
    const updateResult = await newUpdateUserProfile(testUserId, {
      last_login: new Date().toISOString(),
    });
    console.log('Update existing user result:', updateResult);
    
    // Clean up test user
    console.log('\n5. Cleaning up test user...');
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', testUserId);
    
    if (error) {
      console.error('Cleanup error:', error);
    } else {
      console.log('Test user cleaned up successfully');
    }
  }
}

// Run the test
testFix().catch(console.error);
